"""
Perfect Contact Extractor - The Ultimate Solution
Combines guaranteed page checking with intelligent early stopping.
"""

import asyncio
import json
import re
import csv
from datetime import datetime
from typing import Dict, List, Optional, Union, Tuple
from urllib.parse import urljoin

from crawl4ai import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CrawlerRunConfig
from crawl4ai import JsonCssExtractionStrategy


class PerfectContactExtractor:
    """
    The perfect contact extractor that combines:
    - Guaranteed page checking (no missed contact pages)
    - Intelligent early stopping (maximum efficiency)
    - Robust extraction (CSS + regex fallbacks)
    """
    
    def __init__(self, batch_size: int = 50, max_concurrent: int = 6):
        """Initialize the perfect extractor."""
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent

        # Adaptive page checking - different priorities for different site types
        self.page_templates = {
            'business': [
                ('', 10),              # Main page - highest priority
                ('/contact-us', 9),    # Primary contact page
                ('/contact', 9),       # Alternative contact page
                ('/about', 7),         # About page often has contact info
            ],
            'government': [
                ('', 10),              # Main page
                ('/contact', 9),       # Contact page
                ('/staff', 8),         # Staff directory
                ('/directory', 8),     # Staff/contact directory
                ('/departments', 7),   # Department listings
                ('/services', 7),      # Service information
                ('/about', 6),         # About page
            ],
            'medical': [
                ('', 10),              # Main page
                ('/contact', 9),       # Contact page
                ('/providers', 8),     # Doctor/provider listings
                ('/doctors', 8),       # Doctor directory
                ('/locations', 8),     # Office locations
                ('/offices', 7),       # Office information
                ('/appointments', 7),  # Appointment info
                ('/patients', 6),      # Patient information
                ('/about', 6),         # About page
            ],
            'education': [
                ('', 10),              # Main page
                ('/contact', 9),       # Contact page
                ('/staff', 8),         # Staff directory
                ('/faculty', 8),       # Faculty listings
                ('/administration', 7), # Admin contacts
                ('/directory', 7),     # Contact directory
                ('/about', 6),         # About page
            ]
        }

    def _detect_website_type(self, url: str) -> str:
        """Detect the type of website to use appropriate page templates."""
        url_lower = url.lower()

        # Government sites
        if '.gov' in url_lower or 'government' in url_lower:
            return 'government'

        # Educational institutions
        if '.edu' in url_lower or any(keyword in url_lower for keyword in ['university', 'college', 'school']):
            return 'education'

        # Medical/healthcare sites
        medical_keywords = [
            'medical', 'health', 'hospital', 'clinic', 'doctor', 'physician',
            'dental', 'dentist', 'orthodont', 'surgery', 'surgical', 'care',
            'wellness', 'therapy', 'treatment', 'patient', 'healthcare',
            'providers', 'upmc', 'mayo', 'cleveland', 'johns-hopkins'
        ]
        if any(keyword in url_lower for keyword in medical_keywords):
            return 'medical'

        # Default to business
        return 'business'

    def _get_adaptive_pages(self, url: str) -> List[Tuple[str, int]]:
        """Get adaptive page list based on website type."""
        website_type = self._detect_website_type(url)
        return self.page_templates.get(website_type, self.page_templates['business'])

    async def extract_perfect(self, urls: Union[str, List[str]]) -> List[Dict]:
        """
        Perfect extraction with guaranteed coverage and smart stopping.
        
        Args:
            urls: URLs to process
            
        Returns:
            List of results with emails and social media
        """
        if isinstance(urls, str):
            urls = [urls]
        
        print(f"⭐ PERFECT CONTACT EXTRACTION")
        print(f"📊 Processing {len(urls)} URLs with guaranteed coverage + smart stopping")
        print(f"⚡ Max concurrent per batch: {self.max_concurrent}")
        
        all_results = []
        total_start = datetime.now()
        
        # Process URLs in batches
        for i in range(0, len(urls), self.batch_size):
            batch = urls[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            total_batches = (len(urls) + self.batch_size - 1) // self.batch_size
            
            print(f"\n📦 Batch {batch_num}/{total_batches}: {len(batch)} URLs")
            
            batch_start = datetime.now()
            batch_results = await self._process_batch_perfect(batch)
            batch_duration = (datetime.now() - batch_start).total_seconds()
            
            all_results.extend(batch_results)
            
            print(f"   ✅ Completed in {batch_duration:.2f}s ({len(batch)/batch_duration:.2f} URLs/sec)")
            
            # Small delay between batches
            if i + self.batch_size < len(urls):
                await asyncio.sleep(1)
        
        total_duration = (datetime.now() - total_start).total_seconds()
        
        print(f"\n🎉 PERFECT EXTRACTION COMPLETED!")
        print(f"   • Total URLs: {len(urls)}")
        print(f"   • Total time: {total_duration:.2f}s ({total_duration/60:.1f} min)")
        print(f"   • Overall rate: {len(urls)/total_duration:.2f} URLs/second")
        
        return all_results
    
    async def _process_batch_perfect(self, urls: List[str]) -> List[Dict]:
        """Process a batch with perfect intelligence."""
        
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_single(url: str) -> Dict:
            async with semaphore:
                return await self._extract_single_perfect(url)
        
        tasks = [process_single(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append({
                    "url": urls[i],
                    "error": str(result),
                    "timestamp": datetime.now().isoformat()
                })
            else:
                final_results.append(result)
        
        return final_results
    
    async def _extract_single_perfect(self, url: str) -> Dict:
        """Extract from single URL with perfect intelligence and adaptive page checking."""

        try:
            # Get adaptive pages based on website type
            adaptive_pages = self._get_adaptive_pages(url)

            # Build pages to check in priority order
            pages_to_check = []
            for page_path, priority in adaptive_pages:
                if page_path == '':
                    page_url = url
                else:
                    page_url = urljoin(url, page_path)
                pages_to_check.append((page_url, priority))
            
            domain = url.split('/')[2].replace('www.', '')
            print(f"   ⭐ {domain}: Checking {len(pages_to_check)} pages with smart stopping")
            
            best_email = None
            best_phone = None
            best_social = None
            pages_checked = 0
            pages_with_content = 0
            
            async with AsyncWebCrawler(verbose=False) as crawler:
                for page_url, priority in pages_to_check:
                    try:
                        print(f"     🔍 Checking: {page_url} (priority: {priority})")
                        
                        # Extract from this page with adaptive timeout
                        # Use shorter timeout for non-main pages to fail fast on problematic sites
                        timeout = 15.0 if page_url == url else 10.0
                        result = await asyncio.wait_for(
                            self._extract_from_page_perfect(crawler, page_url),
                            timeout=timeout
                        )
                        
                        pages_checked += 1
                        
                        if result.get('has_content'):
                            pages_with_content += 1
                        
                        # Update best contacts
                        if result.get('email') and not best_email:
                            best_email = result['email']
                            print(f"     📧 Found email: {best_email['email']}")

                        if result.get('phone') and not best_phone:
                            best_phone = result['phone']
                            print(f"     📞 Found phone: {best_phone['phone']}")

                        if result.get('social') and not best_social:
                            best_social = result['social']
                            print(f"     🌐 Found social: {best_social['platform']}")

                        # Smart early stopping logic - stop when we have email + (phone OR social)
                        if best_email and (best_phone or best_social):
                            contact_types = []
                            if best_phone:
                                contact_types.append("phone")
                            if best_social:
                                contact_types.append("social")
                            print(f"     ⚡ Perfect stop: Found email + {'/'.join(contact_types)} after {pages_checked} pages")
                            break
                        elif pages_checked >= 2 and best_email:
                            # If we have email after checking main + contact page, that's often enough
                            print(f"     ⚡ Smart stop: Have email after checking key pages")
                            # But continue if we haven't checked contact-us page yet
                            remaining_contact_pages = [p for p in pages_to_check[pages_checked:] if 'contact' in p[0]]
                            if not remaining_contact_pages:
                                break
                        
                    except asyncio.TimeoutError:
                        print(f"     ⏰ Timeout: {page_url}")
                        continue
                    except Exception as e:
                        error_msg = str(e)
                        # Handle specific network connectivity issues with cleaner messages
                        if "ERR_CONNECTION_CLOSED" in error_msg or "ERR_CONNECTION_REFUSED" in error_msg:
                            print(f"     🌐 Connection failed: {page_url} - website may be down")
                        elif "ERR_NAME_NOT_RESOLVED" in error_msg:
                            print(f"     🌐 DNS failed: {page_url} - domain not found")
                        elif "ERR_ABORTED" in error_msg:
                            print(f"     🌐 Connection aborted: {page_url} - page redirected or closed")
                        elif "ERR_CONNECTION_TIMED_OUT" in error_msg:
                            print(f"     🌐 Timeout: {page_url} - website too slow")
                        elif "ERR_SSL_PROTOCOL_ERROR" in error_msg:
                            print(f"     🌐 SSL error: {page_url} - certificate issue")
                        elif "net::" in error_msg:
                            try:
                                net_error = error_msg.split('net::')[1].split(' ')[0].split('\n')[0]
                                print(f"     🌐 Network error: {page_url} - {net_error}")
                            except:
                                print(f"     🌐 Network error: {page_url} - connection failed")
                        elif "RuntimeError" in error_msg and "Failed on navigating" in error_msg:
                            print(f"     🌐 Navigation failed: {page_url} - website unreachable")
                        else:
                            print(f"     ❌ Error: {page_url} - {str(e)[:100]}")
                        continue
            
            # Dynamic page discovery fallback - if we found content but no contact info, try to discover pages
            if pages_with_content > 0 and not best_email and not best_phone and not best_social:
                print(f"     🔍 Found content but no contact info, trying dynamic discovery...")
                discovered_pages = await self._discover_pages_dynamically(crawler, url)

                if discovered_pages:
                    print(f"     📋 Discovered {len(discovered_pages)} additional pages to check")

                    for page_url in discovered_pages[:3]:  # Limit to 3 additional pages
                        try:
                            print(f"     🔍 Checking discovered: {page_url}")
                            result = await asyncio.wait_for(
                                self._extract_from_page_perfect(crawler, page_url),
                                timeout=10.0
                            )

                            pages_checked += 1

                            if result.get('has_content'):
                                pages_with_content += 1

                            # Update best contacts
                            if result.get('email') and not best_email:
                                best_email = result['email']
                                print(f"     📧 Found email in discovered page: {best_email['email']}")

                            if result.get('phone') and not best_phone:
                                best_phone = result['phone']
                                print(f"     📞 Found phone in discovered page: {best_phone['phone']}")

                            if result.get('social') and not best_social:
                                best_social = result['social']
                                print(f"     🌐 Found social in discovered page: {best_social['platform']}")

                            # Stop if we found email
                            if best_email:
                                print(f"     ⚡ Success with dynamic discovery!")
                                break

                        except Exception as e:
                            print(f"     ❌ Error checking discovered page: {page_url}")
                            continue

            print(f"     ✅ {domain}: Checked {pages_checked}/{len(pages_to_check) + (3 if pages_with_content == 0 else 0)} pages, {pages_with_content} had content")

            return {
                "url": url,
                "timestamp": datetime.now().isoformat(),
                "email": best_email,
                "phone": best_phone,
                "social_media": best_social,
                "pages_available": len(pages_to_check),
                "pages_checked": pages_checked,
                "pages_with_content": pages_with_content,
                "efficiency": f"{pages_checked}/{len(pages_to_check)}",
                "success": True
            }
        
        except Exception as e:
            error_msg = str(e)
            # Handle specific network connectivity issues with better messaging
            if "ERR_CONNECTION_CLOSED" in error_msg or "ERR_CONNECTION_REFUSED" in error_msg:
                error_msg = "Connection failed - website unreachable"
            elif "ERR_NAME_NOT_RESOLVED" in error_msg:
                error_msg = "DNS resolution failed - domain not found"
            elif "ERR_ABORTED" in error_msg:
                error_msg = "Connection aborted - page may have redirected or closed"
            elif "ERR_CONNECTION_TIMED_OUT" in error_msg:
                error_msg = "Connection timeout - website too slow"
            elif "ERR_SSL_PROTOCOL_ERROR" in error_msg:
                error_msg = "SSL certificate error"
            elif "net::" in error_msg:
                # Extract the specific network error
                try:
                    net_error = error_msg.split('net::')[1].split(' ')[0].split('\n')[0]
                    error_msg = f"Network error - {net_error}"
                except:
                    error_msg = "Network error - connection failed"
            elif "RuntimeError" in error_msg and "Failed on navigating" in error_msg:
                error_msg = "Navigation failed - website unreachable"

            return {
                "url": url,
                "error": error_msg,
                "timestamp": datetime.now().isoformat()
            }
    
    async def _extract_from_page_perfect(self, crawler: AsyncWebCrawler, url: str) -> Dict:
        """Perfect extraction with comprehensive strategies."""
        
        # Enhanced CSS extraction strategy with comprehensive social media support
        css_schema = {
            "name": "Perfect Contact",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "emails",
                    "selector": "a[href^='mailto:'], [data-email], [href*='@'], .email, .contact-email",
                    "type": "list",
                    "fields": [{"name": "email", "type": "attribute", "attribute": "href"}]
                },
                {
                    "name": "phones",
                    "selector": "a[href^='tel:'], [data-phone], .phone, .contact-phone, .telephone",
                    "type": "list",
                    "fields": [{"name": "phone", "type": "attribute", "attribute": "href"}]
                },
                {
                    "name": "social",
                    "selector": "a[href*='instagram.com'], a[href*='facebook.com'], a[href*='twitter.com'], a[href*='x.com'], a[href*='linkedin.com'], a[href*='youtube.com'], a[href*='tiktok.com'], a[href*='pinterest.com'], a[href*='yelp.com'], .social-links a, .social-media a, [class*='social'] a",
                    "type": "list",
                    "fields": [{"name": "url", "type": "attribute", "attribute": "href"}]
                }
            ]
        }
        
        strategy = JsonCssExtractionStrategy(css_schema, verbose=False)
        config = CrawlerRunConfig(extraction_strategy=strategy)

        try:
            result = await crawler.arun(url=url, config=config)
        except Exception as e:
            # Handle all crawler-specific errors gracefully
            error_msg = str(e)
            # Return empty result for any crawler error to prevent crashes
            return {'email': None, 'phone': None, 'social': None, 'has_content': False}

        best_email = None
        best_social = None
        best_phone = None
        has_content = False

        # Check if page was successfully loaded
        if result.success and result.status_code and result.status_code < 400:
            has_content = True
        elif result.status_code == 404:
            # Page doesn't exist - this is expected for some pages
            return {'email': None, 'phone': None, 'social': None, 'has_content': False}
        elif result.success:
            has_content = True
            
            # Process CSS results
            if result.extracted_content:
                try:
                    data = json.loads(result.extracted_content)
                    if data and len(data) > 0:
                        css_data = data[0]
                        
                        # Extract emails
                        emails = set()
                        for email_item in css_data.get('emails', []):
                            if isinstance(email_item, dict):
                                email = email_item.get('email', '').replace('mailto:', '').strip().lower()
                                if self._is_valid_email(email):
                                    emails.add(email)
                        
                        if emails:
                            best_email = self._select_best_email(list(emails))

                        # Extract phone numbers
                        phones = set()
                        for phone_item in css_data.get('phones', []):
                            if isinstance(phone_item, dict):
                                phone = phone_item.get('phone', '').replace('tel:', '').strip()
                                if self._is_valid_phone(phone):
                                    phones.add(phone)

                        if phones:
                            best_phone = self._select_best_phone(list(phones))

                        # Extract social links
                        social_links = set()
                        for social_item in css_data.get('social', []):
                            if isinstance(social_item, dict):
                                social_url = social_item.get('url', '').strip()
                                if social_url and self._is_valid_social_url(social_url):
                                    social_links.add(social_url)

                        if social_links:
                            best_social = self._select_best_social(list(social_links))
                
                except json.JSONDecodeError:
                    pass
            
            # Perfect fallback: Comprehensive regex extraction
            if (not best_email or not best_social) and hasattr(result, 'cleaned_html'):
                content = result.cleaned_html
                
                # Perfect email regex patterns
                if not best_email:
                    email_patterns = [
                        r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b',  # Standard email
                        r'(?:email|contact|reach|write|talk)[\s:]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',  # After keywords
                        r'mailto:([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',  # Mailto links
                    ]
                    
                    all_emails = set()
                    for pattern in email_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        for match in matches:
                            email = match if isinstance(match, str) else match[0] if isinstance(match, tuple) else str(match)
                            if self._is_valid_email(email.lower()):
                                all_emails.add(email.lower())
                    
                    if all_emails:
                        best_email = self._select_best_email(list(all_emails))
                
                # Perfect phone regex
                if not best_phone:
                    phone_patterns = [
                        r'(?:phone|tel|call)[\s:]*(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})',  # After keywords
                        r'(\d{3}[-.\s]\d{3}[-.\s]\d{4})',  # ************
                        r'(\(\d{3}\)\s?\d{3}[-.\s]\d{4})',  # (*************
                        r'(\+?1[-.\s]?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})',  # +1 formats
                    ]

                    all_phones = set()
                    for pattern in phone_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        for match in matches:
                            phone = match if isinstance(match, str) else match[0] if isinstance(match, tuple) else str(match)
                            if self._is_valid_phone(phone):
                                all_phones.add(phone)

                    if all_phones:
                        best_phone = self._select_best_phone(list(all_phones))

                # Perfect social regex with expanded platforms
                if not best_social:
                    social_pattern = r'https?://(?:www\.)?(?:instagram|facebook|twitter|x|linkedin|youtube|tiktok|pinterest|yelp)\.com/[^\s<>"\']+'
                    social_matches = re.findall(social_pattern, content, re.IGNORECASE)
                    valid_socials = [s for s in social_matches[:5] if self._is_valid_social_url(s)]
                    if valid_socials:
                        best_social = self._select_best_social(valid_socials)
        
        return {
            'email': best_email,
            'phone': best_phone,
            'social': best_social,
            'has_content': has_content
        }
    
    def _is_valid_email(self, email: str) -> bool:
        """Perfect email validation."""
        if not email or len(email) < 5 or len(email) > 50:
            return False
        
        # Enhanced blacklist
        blacklist = ['.svg', '.png', '.jpg', '.jpeg', '.gif', '.css', '.js', '.pdf', '.doc', '.zip']
        if any(ext in email for ext in blacklist):
            return False
        
        # Must have exactly one @
        if '@' not in email or email.count('@') != 1:
            return False
        
        # Perfect format validation
        return bool(re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email))
    
    def _is_valid_social_url(self, url: str) -> bool:
        """Perfect social media URL validation with comprehensive platform support."""
        if not url or len(url) < 10:
            return False

        # Expanded platform support
        platforms = [
            'instagram.com', 'facebook.com', 'twitter.com', 'x.com',
            'linkedin.com', 'youtube.com', 'tiktok.com', 'pinterest.com',
            'yelp.com', 'google.com/maps'  # Google My Business
        ]
        if not any(platform in url.lower() for platform in platforms):
            return False

        # Avoid post/photo/status URLs and other unwanted patterns
        avoid_patterns = [
            '/p/', '/posts/', '/photo/', '/status/', '/tweet/', '/reel/',
            '/watch?v=', '/pin/', '/review/', '/photos/', '/events/',
            '/ads/', '/business/help/', '/privacy/', '/terms/', '/support/'
        ]
        if any(pattern in url.lower() for pattern in avoid_patterns):
            return False

        # Avoid obviously non-business URLs
        if any(word in url.lower() for word in ['sharer.php', 'intent/tweet', 'share?']):
            return False

        return True

    def _is_valid_phone(self, phone: str) -> bool:
        """Validate phone number."""
        if not phone:
            return False

        # Remove all non-digit characters for validation
        digits_only = re.sub(r'\D', '', phone)

        # Valid phone numbers should have 10 or 11 digits
        if not (10 <= len(digits_only) <= 11):
            return False

        # Avoid obviously invalid patterns
        if digits_only in ['0000000000', '1111111111', '1234567890']:
            return False

        return True

    def _select_best_phone(self, phones: List[str]) -> Optional[Dict]:
        """Select the best phone number from candidates."""
        if not phones:
            return None

        # Prefer formatted phone numbers
        formatted_phones = [p for p in phones if any(char in p for char in ['-', '(', ')', '.', ' '])]
        if formatted_phones:
            best_phone = formatted_phones[0]
        else:
            best_phone = phones[0]

        return {"phone": best_phone, "confidence": 0.90}

    def _select_best_email(self, emails: List[str]) -> Optional[Dict]:
        """Perfect email selection with enhanced priority."""
        if not emails:
            return None
        
        # Perfect priority prefixes
        priority_prefixes = ['contact', 'info', 'hello', 'admin', 'support', 'talk', 'team']
        
        for prefix in priority_prefixes:
            for email in emails:
                if email.startswith(f"{prefix}@"):
                    return {"email": email, "confidence": 0.95}
        
        # Return shortest, most professional email
        best = min(emails, key=lambda x: (len(x), x.count('.'), 'noreply' in x))
        confidence = 0.85 if 'noreply' not in best else 0.70
        return {"email": best, "confidence": confidence}
    
    def _select_best_social(self, social_links: List[str]) -> Optional[Dict]:
        """Perfect social media selection with comprehensive platform support."""
        if not social_links:
            return None

        # Platform priority (business-focused order)
        priority = [
            'instagram.com', 'facebook.com', 'linkedin.com',
            'twitter.com', 'x.com', 'youtube.com', 'yelp.com',
            'tiktok.com', 'pinterest.com', 'google.com/maps'
        ]

        for platform in priority:
            for link in social_links:
                if platform in link.lower():
                    platform_name = self._normalize_platform_name(platform)
                    handle = self._extract_handle(link, platform_name)
                    confidence = self._get_platform_confidence(platform_name)
                    return {
                        "platform": platform_name,
                        "url": link,
                        "handle": handle,
                        "confidence": confidence
                    }

        return None

    def _normalize_platform_name(self, platform: str) -> str:
        """Normalize platform names for consistency."""
        platform_map = {
            'instagram.com': 'instagram',
            'facebook.com': 'facebook',
            'linkedin.com': 'linkedin',
            'twitter.com': 'twitter',
            'x.com': 'twitter',  # Normalize X to Twitter
            'youtube.com': 'youtube',
            'tiktok.com': 'tiktok',
            'pinterest.com': 'pinterest',
            'yelp.com': 'yelp',
            'google.com/maps': 'google_business'
        }
        return platform_map.get(platform, platform.split('.')[0])

    def _get_platform_confidence(self, platform: str) -> float:
        """Get confidence score based on platform business relevance."""
        confidence_map = {
            'instagram': 0.95,
            'facebook': 0.95,
            'linkedin': 0.90,
            'twitter': 0.85,
            'youtube': 0.80,
            'yelp': 0.90,
            'tiktok': 0.75,
            'pinterest': 0.70,
            'google_business': 0.95
        }
        return confidence_map.get(platform, 0.80)
    
    def _extract_handle(self, url: str, platform: str = '') -> Optional[str]:
        """Extract social media handle with platform-specific logic."""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            path_parts = [p for p in parsed.path.strip('/').split('/') if p]

            if not path_parts:
                return None

            # Platform-specific handle extraction
            if platform == 'youtube':
                # YouTube channels: /channel/UCxxx or /c/channelname or /user/username
                if path_parts[0] in ['channel', 'c', 'user'] and len(path_parts) > 1:
                    return f"@{path_parts[1]}"
                elif path_parts[0] not in ['watch', 'playlist', 'shorts']:
                    return f"@{path_parts[0]}"
            elif platform == 'linkedin':
                # LinkedIn: /company/companyname or /in/username
                if path_parts[0] in ['company', 'in'] and len(path_parts) > 1:
                    return f"@{path_parts[1]}"
            elif platform == 'yelp':
                # Yelp: /biz/business-name
                if path_parts[0] == 'biz' and len(path_parts) > 1:
                    return f"@{path_parts[1]}"
            elif platform == 'google_business':
                # Google My Business: extract business name from URL
                if 'place' in path_parts:
                    return "@google_business"
            else:
                # Standard social media platforms
                if path_parts[0] not in ['share', 'sharer', 'intent', 'pages']:
                    return f"@{path_parts[0]}"

            return None
        except:
            pass
        return None

    async def _discover_pages_dynamically(self, crawler: AsyncWebCrawler, base_url: str) -> List[str]:
        """Dynamically discover pages by analyzing the main page for relevant links."""
        try:
            # Extract from main page to find links
            config = CrawlerRunConfig()
            result = await crawler.arun(url=base_url, config=config)

            if not result.success or not result.cleaned_html:
                return []

            content = result.cleaned_html
            discovered_pages = set()

            # Look for common contact-related links
            contact_patterns = [
                r'href=["\']([^"\']*(?:contact|staff|directory|team|about|location|office|provider|doctor|appointment)[^"\']*)["\']',
                r'href=["\']([^"\']*(?:phone|email|reach|connect|support|help)[^"\']*)["\']',
            ]

            for pattern in contact_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    # Clean and validate the URL
                    if match.startswith('/'):
                        full_url = urljoin(base_url, match)
                    elif match.startswith('http'):
                        # Only include URLs from the same domain
                        if base_url.split('/')[2] in match:
                            full_url = match
                        else:
                            continue
                    else:
                        full_url = urljoin(base_url, match)

                    # Filter out unwanted links
                    if self._is_valid_discovered_page(full_url, base_url):
                        discovered_pages.add(full_url)

            return list(discovered_pages)[:5]  # Limit to 5 discovered pages

        except Exception:
            return []

    def _is_valid_discovered_page(self, page_url: str, base_url: str) -> bool:
        """Check if a discovered page is worth checking."""
        page_lower = page_url.lower()

        # Skip unwanted file types and pages
        skip_patterns = [
            '.pdf', '.doc', '.jpg', '.png', '.gif', '.css', '.js',
            'javascript:', 'mailto:', 'tel:', '#',
            'login', 'register', 'cart', 'checkout', 'search',
            'privacy', 'terms', 'cookie', 'sitemap'
        ]

        if any(pattern in page_lower for pattern in skip_patterns):
            return False

        # Must be from same domain
        try:
            base_domain = base_url.split('/')[2].replace('www.', '')
            page_domain = page_url.split('/')[2].replace('www.', '')
            if base_domain != page_domain:
                return False
        except:
            return False

        # Should contain relevant keywords
        relevant_keywords = [
            'contact', 'staff', 'directory', 'team', 'about', 'location',
            'office', 'provider', 'doctor', 'appointment', 'phone', 'email'
        ]

        return any(keyword in page_lower for keyword in relevant_keywords)

    def export_to_csv(self, results: List[Dict], filename: str):
        """Export results to CSV with perfect details."""
        if not results:
            return
        
        rows = []
        for result in results:
            if 'error' in result:
                continue
            
            email = result.get('email', {})
            phone = result.get('phone', {})
            social = result.get('social_media', {})

            row = {
                'url': result.get('url', ''),
                'email': email.get('email', '') if email else '',
                'email_confidence': email.get('confidence', '') if email else '',
                'phone': phone.get('phone', '') if phone else '',
                'phone_confidence': phone.get('confidence', '') if phone else '',
                'social_platform': social.get('platform', '') if social else '',
                'social_handle': social.get('handle', '') if social else '',
                'social_url': social.get('url', '') if social else '',
                'social_confidence': social.get('confidence', '') if social else '',
                'pages_available': result.get('pages_available', 0),
                'pages_checked': result.get('pages_checked', 0),
                'pages_with_content': result.get('pages_with_content', 0),
                'efficiency': result.get('efficiency', ''),
                'timestamp': result.get('timestamp', '')
            }
            rows.append(row)
        
        if rows:
            fieldnames = list(rows[0].keys())
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(rows)
            print(f"📊 Results exported to {filename}")
    
    def print_summary(self, results: List[Dict]):
        """Print perfect extraction summary."""
        total = len(results)
        successful = len([r for r in results if 'error' not in r])
        errors = total - successful
        
        emails_found = len([r for r in results if 'error' not in r and r.get('email')])
        socials_found = len([r for r in results if 'error' not in r and r.get('social_media')])
        
        total_available = sum(r.get('pages_available', 0) for r in results if 'error' not in r)
        total_checked = sum(r.get('pages_checked', 0) for r in results if 'error' not in r)
        
        avg_available = total_available / successful if successful > 0 else 0
        avg_checked = total_checked / successful if successful > 0 else 0
        efficiency = (1 - (total_checked / total_available)) * 100 if total_available > 0 else 0
        
        early_stops = len([r for r in results if 'error' not in r and r.get('pages_checked', 0) < r.get('pages_available', 0)])
        
        print(f"\n📊 PERFECT EXTRACTION SUMMARY:")
        print(f"   • Total URLs: {total}")
        print(f"   • Successful: {successful}")
        print(f"   • Errors: {errors}")
        
        if successful > 0:
            print(f"   • Emails found: {emails_found}/{successful} ({emails_found/successful*100:.1f}%)")
            print(f"   • Social media found: {socials_found}/{successful} ({socials_found/successful*100:.1f}%)")
            print(f"   • Both found: {len([r for r in results if 'error' not in r and r.get('email') and r.get('social_media')])}/{successful}")
            print(f"   • Average pages available: {avg_available:.1f}")
            print(f"   • Average pages checked: {avg_checked:.1f}")
            print(f"   • Early stops: {early_stops}/{successful} ({early_stops/successful*100:.1f}%)")
            print(f"   • Efficiency gain: {efficiency:.1f}% fewer pages checked")


# Perfect function for production use
async def extract_contacts_perfect(urls: Union[str, List[str]], 
                                 batch_size: int = 50,
                                 max_concurrent: int = 6) -> List[Dict]:
    """
    Perfect contact extraction with guaranteed coverage and smart stopping.
    
    Args:
        urls: URLs to process
        batch_size: URLs per batch
        max_concurrent: Concurrent requests per batch
        
    Returns:
        List of extraction results
    """
    extractor = PerfectContactExtractor(batch_size=batch_size, max_concurrent=max_concurrent)
    return await extractor.extract_perfect(urls)
